package com.sky.controller.user;

import com.sky.entity.Category;
import com.sky.result.Result;
import com.sky.service.CategoryQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/user/category")
@Slf4j
@Api(tags = "用户端-分类接口", value = "用户端分类相关接口", description = "提供分类查询相关接口")
public class CategoryQueryController {

    @Autowired
    private CategoryQueryService categoryQueryService;

    @ApiOperation(value = "查询分类列表", notes = "根据类型查询分类列表，1为菜品分类，2为套餐分类")
    @ApiImplicitParam(name = "type", value = "分类类型", required = false, dataType = "Integer", paramType = "query", example = "1")
    @GetMapping("/list")
    public Result<List<Category>> findAll(Integer type) {
        log.info("查询所有分类");
        List<Category> list = categoryQueryService.findByType(type);
        return Result.success(list);
    }
}
