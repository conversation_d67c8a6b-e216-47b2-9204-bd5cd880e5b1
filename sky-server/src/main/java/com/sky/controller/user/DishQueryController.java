package com.sky.controller.user;

import com.sky.result.Result;
import com.sky.service.DishQueryService;
import com.sky.vo.DishVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/user/dish")
@Slf4j
@Api(tags = "用户端-菜品接口", value = "用户端菜品相关接口", description = "提供菜品查询相关接口")
public class DishQueryController {

    private final DishQueryService dishQueryService;

    private final RedisTemplate redisTemplate;

    @Autowired
    public DishQueryController(DishQueryService dishQueryService, RedisTemplate redisTemplate) {
        this.dishQueryService = dishQueryService;
        this.redisTemplate = redisTemplate;
    }

    @ApiOperation(value = "根据分类ID查询菜品列表", notes = "查询指定分类下的所有菜品信息")
    @ApiImplicitParam(name = "categoryId", value = "分类ID", required = true, dataType = "Long", paramType = "query", example = "1")
    @GetMapping("/list")
    @Cacheable(cacheNames = "dishCache", key = "#categoryId")
    public Result<List<DishVO>> findByCategoryId(Long categoryId) {
        //构造redis中的key
        String key="dish_"+categoryId;
        //查询redis中是否存在菜品数据
        final List<DishVO> list = (List<DishVO>)redisTemplate.opsForValue().get(key);
        if(list!=null&& !list.isEmpty()){
            //说明存在缓存数据，直接返回
            System.out.println("redis调用成功！");
            return Result.success(list);
        }

        log.info("根据分类id查询菜品：{}", categoryId);
        List<DishVO> dishVOList = dishQueryService.findByCategoryId(categoryId);
        redisTemplate.opsForValue().set(key,dishVOList);
        return Result.success(dishVOList);
    }

}
