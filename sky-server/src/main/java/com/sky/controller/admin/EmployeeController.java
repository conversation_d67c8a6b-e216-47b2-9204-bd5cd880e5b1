package com.sky.controller.admin;

import com.sky.constant.JwtClaimsConstant;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.dto.PasswordEditDTO;
import com.sky.entity.Employee;
import com.sky.properties.JwtProperties;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.EmployeeService;
import com.sky.service.impl.EmployeeServiceImpl;
import com.sky.utils.JwtUtil;
import com.sky.vo.EmployeeLoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 员工管理
 */
@RestController
@RequestMapping("/admin/employee")
@Slf4j
@Api(tags = "管理端-员工管理", value = "员工相关接口", description = "提供员工的增删改查等相关接口")
public class EmployeeController {

    private final EmployeeService employeeService;

    private final JwtProperties jwtProperties;
    @Autowired
    public EmployeeController(EmployeeService employeeService, JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
        this.employeeService = employeeService;
    }

    /**
     * 登录
     *
     * @param employeeLoginDTO
     * @return
     */
    @ApiOperation(value = "员工登录", notes = "员工登录接口，登录成功后返回令牌")
    @PostMapping("/login")
    public Result<EmployeeLoginVO> login(@RequestBody EmployeeLoginDTO employeeLoginDTO, HttpServletResponse response) {
        log.info("员工登录：{}", employeeLoginDTO);

        Employee employee = employeeService.login(employeeLoginDTO);

        //登录成功后，生成jwt令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaimsConstant.EMP_ID, employee.getId());
        String token = JwtUtil.createJWT(
                jwtProperties.getAdminSecretKey(),
                jwtProperties.getAdminTtl(),
                claims);

        // 将JWT token设置为HttpOnly Cookie
        Cookie jwtCookie = new Cookie("admin_jwt_token", token);
        jwtCookie.setHttpOnly(true);
        jwtCookie.setPath("/");
        jwtCookie.setMaxAge((int) (jwtProperties.getAdminTtl() / 1000)); // 转换为秒
        response.addCookie(jwtCookie);

        EmployeeLoginVO employeeLoginVO = EmployeeLoginVO.builder()
                .id(employee.getId())
                .userName(employee.getUsername())
                .name(employee.getName())
                .token(token)
                .build();

        return Result.success(employeeLoginVO);
    }

    /**
     * 退出
     *
     * @return
     */
    @ApiOperation(value = "员工退出", notes = "员工退出登录状态")
    @PostMapping("/logout")
    public Result<String> logout(HttpServletResponse response) {
        // 清除JWT Cookie
        Cookie jwtCookie = new Cookie("admin_jwt_token", "");
        jwtCookie.setHttpOnly(true);
        jwtCookie.setPath("/");
        jwtCookie.setMaxAge(0); // 立即过期
        response.addCookie(jwtCookie);

        return Result.success();
    }

    @ApiOperation(value = "员工分页查询", notes = "根据条件分页查询员工信息")
    @GetMapping("/page")
    public Result<PageResult> findAllByPage(EmployeePageQueryDTO employeePageQueryDTO) {
        log.info("分页查询：{}", employeePageQueryDTO);
        return Result.success(employeeService.findAllByPage(employeePageQueryDTO));


    }

    @ApiOperation(value = "新增员工", notes = "新增员工信息")
    @PostMapping
    public Result saveEmp(@RequestBody EmployeeDTO employeedto) {
        log.info("新增员工：{}", employeedto);
        employeeService.saveEmp(employeedto);
        return Result.success();
    }

    @ApiOperation(value = "启用禁用员工账号", notes = "启用或禁用员工账号")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "status", value = "状态，1启用，0禁用", required = true, dataType = "Integer", paramType = "path"),
        @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "Long", paramType = "query")
    })
    @PostMapping("/status/{status}")
    public Result updateStatus(@PathVariable Integer status, Long id) {
        log.info("修改员工状态：{}", status);
        log.info("修改员工id：{}", id);
        employeeService.updateStatus(status, id);
        return Result.success();
    }

    @ApiOperation(value = "编辑员工", notes = "编辑员工信息")
    @PutMapping
    public Result update(@RequestBody EmployeeDTO employeeDTO) {
        log.info("修改员工信息：{}", employeeDTO);
        employeeService.updateEmp(employeeDTO);
        return Result.success();
    }

    @ApiOperation(value = "根据ID查询员工", notes = "根据员工ID查询员工信息")
    @ApiImplicitParam(name = "id", value = "员工ID", required = true, dataType = "Long", paramType = "path")
    @GetMapping("/{id}")
    public Result<Employee> getById(@PathVariable Long id) {
        log.info("根据id查询员工：{}", id);
        Employee employee = employeeService.findById(id);
        return Result.success(employee);
    }

    @ApiOperation(value = "修改员工密码", notes = "修改员工登录密码")
    @PutMapping("/editPassword")
    public Result updatePassword(@RequestBody PasswordEditDTO passwordEditDTO) {
        log.info("修改密码：{}", passwordEditDTO);
        employeeService.updatePassword(passwordEditDTO);
        return Result.success();
    }
}
