package com.sky.controller.admin;

import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.SetMealService;
import com.sky.vo.SetmealVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Delete;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/admin/setmeal")
@Api(tags = "管理端-套餐管理", value = "套餐相关接口", description = "提供套餐的增删改查等相关接口")
public class SetMealController {
    //这个类下包含的方法如下
    //1.分页查询
    //2.新增套餐
    //3.修改套餐
    //4.根据id查询套餐
    //5.修改套餐状态
    //6.删除套餐

    @Autowired
    private SetMealService setMealService;

    @ApiOperation(value = "套餐分页查询", notes = "根据条件分页查询套餐信息")
    @RequestMapping("/page")
    @Cacheable(cacheNames = "setmealCache", key = "#setMealPageQueryDTO")
    public Result<PageResult> findAllByPage(SetmealPageQueryDTO setMealPageQueryDTO) {
        log.info("分页查询{}", setMealPageQueryDTO);
        return Result.success(setMealService.findAllByPage(setMealPageQueryDTO));
    }

    @ApiOperation(value = "新增套餐", notes = "新增套餐及其包含的菜品")
    @PostMapping
    @CacheEvict(cacheNames = "setmealCache",key = "setmealDTO.categoryId")
    public Result saveSetMeal(@RequestBody SetmealDTO setmealDTO) {
        log.info("新增套餐{}", setmealDTO);
        setMealService.saveSetMeal(setmealDTO);
        return Result.success();
    }

    @ApiOperation(value = "编辑套餐", notes = "编辑套餐及其包含的菜品")
    @PutMapping
    @CacheEvict(cacheNames = "setmealCache", allEntries = true)
    public Result updateSetMeal(@RequestBody SetmealDTO setmealDTO) {
        log.info("修改套餐{}", setmealDTO);
        setMealService.updateSetMeal(setmealDTO);
        return Result.success();
    }

    @ApiOperation(value = "根据ID查询套餐", notes = "根据套餐ID查询套餐信息")
    @ApiImplicitParam(name = "id", value = "套餐ID", required = true, dataType = "Long", paramType = "path")
    @GetMapping("/{id}")
    @Cacheable(cacheNames = "setmealCache", key = "#id")
    public Result<SetmealVO> getById(@PathVariable Long id) {
        log.info("根据id查询套餐{}", id);
        SetmealVO setmealVO = setMealService.findById(id);
        return Result.success(setmealVO);
    }

    @ApiOperation(value = "套餐起售停售", notes = "套餐起售停售操作")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "status", value = "状态，1起售，0停售", required = true, dataType = "Integer", paramType = "path"),
        @ApiImplicitParam(name = "id", value = "套餐ID", required = true, dataType = "Long", paramType = "query")
    })
    @PostMapping("/status/{status}")
    @CacheEvict(cacheNames = "setmealCache", allEntries = true)
    public Result updateStatus(@PathVariable Integer status, Long id) {
        log.info("修改套餐状态{}", status);
        setMealService.updateStatus(status, id);
        return Result.success();
    }

    @ApiOperation(value = "批量删除套餐", notes = "根据ID批量删除套餐")
    @ApiImplicitParam(name = "ids", value = "套餐ID列表", required = true, dataType = "List", paramType = "query")
    @DeleteMapping
    @CacheEvict(cacheNames = "setmealCache",allEntries = true)
    public Result deleteSetMeal(@RequestParam("ids") List<Long> ids) {
        log.info("删除套餐{}", ids);
        setMealService.deleteSetMeal(ids);
        return Result.success();
    }
}
