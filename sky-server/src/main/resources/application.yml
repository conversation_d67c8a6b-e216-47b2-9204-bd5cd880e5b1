server:
  port: 8080



spring:
  profiles:
    active: test
    include:
      - env # 在当前目录下新建 application-env.yml 配置敏感信息
  main:
    allow-circular-references: true

  datasource:
    driver-class-name: ${sky.datasource.driver-class-name} # 驱动名称
    url: ${sky.datasource.url}
    username: ${sky.datasource.username}
    password: ${sky.datasource.password}
    type: ${sky.datasource.type}

  redis:
      host: ${sky.redis.host}
      port: ${sky.redis.port}
      password: ${sky.redis.password}
      database: ${sky.redis.database}

# knife4j配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: "default"
      paths-to-match: "/**"
      packages-to-scan: com.github.xiaoymin.boot3
  default-flat-param-object: true

knife4j:
  enable: true
  setting:
    language: zh-CN

mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  #为该包中的类，自动设置别名
  type-aliases-package: com.sky.entity
  configuration:
    #开启驼峰命名
    map-underscore-to-camel-case: true
    database-id: mysql


logging:
  level:
    com:
      sky:
        mapper: debug
        service: info
        controller: info

sky:
  jwt:
    admin-secret-key: admin
    admin-ttl: 7200000
    admin-token-name: Authorization # 这里修改为大写的 "Authorization"
    user-secret-key: user
    user-ttl: 7200000
    user-token-name: authentication
  oss:
  endpoint: ${sky.oss.endpoint}
  bucket-name: ${sky.oss.bucket-name}
  access-key-id: ${sky.oss.access-key-id}
  access-key-secret: ${sky.oss.access-key-secret}

  wechat:
    appid: ${sky.wechat.appid}
    secret: ${sky.wechat.secret}
    mchid: ${sky.wechat.mchid}
    mchSerialNo: ${sky.wechat.mchSerialNo}
    privateKeyFilePath: ${sky.wechat.privateKeyFilePath}
    apiV3Key: ${sky.wechat.apiV3Key}
    weChatPayCertFilePath: ${sky.wechat.weChatPayCertFilePath}
    notifyUrl: ${sky.wechat.notifyUrl}